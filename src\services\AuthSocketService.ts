import { getDataToAsyncStorage } from '../utils/AsyncStorage';
import { StorageContanst } from '../Config/Contanst';
import SocketService from '../modules/chat/services/SocketService';
import store from '../redux/store/store';
import { connectSocket, disconnectSocket } from '../redux/reducers/ChatReducer';

/**
 * Service để quản lý kết nối socket khi user đăng nhập/đăng xuất
 */
class AuthSocketService {
  private isInitialized = false;
  private isConnecting = false; // Flag để tránh multiple concurrent connections
  private currentUserId: string | null = null;

  /**
   * Khởi tạo kết nối socket khi user đã đăng nhập
   * Được gọi khi:
   * - App khởi động và có token
   * - User đăng nhập thành công
   * - User đăng nhập bằng social
   */
  async initializeSocketConnection(userId?: string,Name?: string) {
    try {
      console.log('🔌 [AuthSocketService] Initializing socket connection...');

      // Tránh multiple concurrent connections
      if (this.isConnecting) {
        console.log('⏳ [AuthSocketService] Connection already in progress, skipping...');
        return false;
      }

      // Lấy thông tin từ AsyncStorage nếu không được truyền vào
      const finalUserId = userId || await this.getUserId();
      if (!finalUserId) {
        console.log('❌ [AuthSocketService] Missing userId for socket connection');
        return false;
      }

      // Kiểm tra xem đã kết nối với cùng user chưa
      if (this.isInitialized && SocketService.getConnectionStatus() && this.currentUserId === finalUserId) {
        console.log('✅ [AuthSocketService] Socket already connected for same user, skipping...');
        return true;
      }

      this.isConnecting = true;

      // Ngắt kết nối cũ nếu có (để tránh multiple connections)
      if (SocketService.getConnectionStatus()) {
        console.log('🔌 [AuthSocketService] Disconnecting existing socket connection...');
        SocketService.disconnect();
        this.isInitialized = false;
        this.currentUserId = null;
        // Đợi một chút để đảm bảo disconnect hoàn tất
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Kết nối socket mới
      console.log(`🔌 [AuthSocketService] Connecting socket for user: ${finalUserId}`);
      await SocketService.connect(finalUserId, Name || '');

      // Cập nhật Redux state
      store.dispatch(connectSocket(finalUserId, Name || ''));

      this.isInitialized = true;
      this.currentUserId = finalUserId;
      this.isConnecting = false;
      console.log('✅ [AuthSocketService] Socket connection initialized successfully');

      return true;
    } catch (error) {
      console.error('❌ [AuthSocketService] Failed to initialize socket connection:', error);
      this.isInitialized = false;
      this.currentUserId = null;
      this.isConnecting = false;
      return false;
    }
  }

  /**
   * Ngắt kết nối socket khi user đăng xuất
   */
  disconnectSocket() {
    try {
      console.log('🔌 [AuthSocketService] Disconnecting socket...');

      SocketService.disconnect();
      store.dispatch(disconnectSocket());

      this.isInitialized = false;
      this.currentUserId = null;
      this.isConnecting = false;
      console.log('✅ [AuthSocketService] Socket disconnected successfully');
    } catch (error) {
      console.error('❌ [AuthSocketService] Failed to disconnect socket:', error);
    }
  }

  /**
   * Kiểm tra trạng thái kết nối socket
   */
  isConnected(): boolean {
    return this.isInitialized && SocketService.getConnectionStatus();
  }

  /**
   * Lấy userId từ Redux store hoặc AsyncStorage
   */
  private async getUserId(): Promise<string | null> {
    try {
      // Lấy từ Redux store trước
      const state = store.getState();
      const customer = state.customer?.data;
      
      if (customer?.id || customer?.Id) {
        return customer.id || customer.Id;
      }

      // Fallback: có thể lấy từ AsyncStorage nếu cần
      // const userId = await getDataToAsyncStorage('userId');
      // return userId;

      return null;
    } catch (error) {
      console.error('Error getting userId:', error);
      return null;
    }
  }

  /**
   * Retry kết nối socket nếu bị mất kết nối
   */
  async retryConnection() {
    if (!this.isConnected()) {
      console.log('🔄 Retrying socket connection...');
      const customer = store.getState().customer.data;
      if (customer?.Id) {
        return await this.initializeSocketConnection(customer.Id, customer.Name);
      }else{
        return false;
      }
    }
    return true;
  }
}

export default new AuthSocketService();
