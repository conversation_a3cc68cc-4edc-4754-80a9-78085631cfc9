import React, {useEffect, useMemo, useRef, useState} from 'react';
import {
  Dimensions,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  ScrollView,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {
  showSnackbar,
  ComponentStatus,
  FLoading,
  Winicon,
  AppButton,
  ListTile,
  FDialog,
  showDialog,
  Checkbox,
} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {DataController} from '../../base/baseController';
import LocalAuthen from '../../features/local-authen/local-authen';
import {navigateReset, RootScreen} from '../../router/router';
import {
  saveDataToAsyncStorage,
  getDataToAsyncStorage,
  removeDataToAsyncStorage,
} from '../../utils/AsyncStorage';
import {randomGID, regexPassWord, Ultis} from '../../utils/Utils';
import {validatePhoneNumber} from '../../utils/validate';
import {
  FAddressPickerForm,
  TextFieldForm,
} from '../Default/form/component-form';
import {useForm} from 'react-hook-form';
import GoogleLogin, {
  webClientId,
} from '../../features/socials-login/GoogleSignIn/GoogleSignIn';
import {CustomerActions} from '../../redux/reducers/CustomerReducer';
import {CustomerStatus, StorageContanst} from '../../Config/Contanst';
import {ProductActions} from '../../redux/reducers/ShoptReducer';
import AuthSocketService from '../../services/AuthSocketService';
// import ConfigAPI from '../../Config/ConfigAPI';
// import { decrypt, encrypt } from '../../utils/CryptoUtils';
// import { de } from 'date-fns/locale';
import AppleSignIn from '../../features/socials-login/AppleSignIn/apple-sign-in';

export default function LoginScreen() {
  const methods = useForm({shouldFocusError: false});
  const navigation = useNavigation<any>();
  const dispatch = useDispatch<any>();
  const [isSignUp, setSignUp] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingG, setLoadingG] = useState(false);
  const [loadingA, setLoadingA] = useState(false);
  const [bio, setBio] = useState<any>('');
  const customerController = new DataController('Customer');

  useEffect(() => {
    getDataToAsyncStorage('Mobile').then(result => {
      methods.setValue('LastMobile', result);
      methods.setValue('Mobile', result);
    });
    getDataToAsyncStorage('spBiometrics').then(rs => {
      if (rs == 'true') {
        getDataToAsyncStorage('Biometrics').then(result => {
          if (result) {
            setBio(result);
          }
        });
      }
    });
    getDataToAsyncStorage('RememberPass').then(rs => {
      if (rs == 'true') {
        getDataToAsyncStorage('Password').then(result => {
          if (result) {
            methods.setValue('Password', result);
          }
        });
      }
    });
  }, []);

  const _loginSocial = async (value: any, type: any) => {
    if (value) {
      console.log('social login success', value);
      let isFirstTime = false;
      setLoading(true);
      if (value?.user?.email && type === 'google') {
        const resEmail = await customerController.getListSimple({
          page: 1,
          size: 1,
          query: `@Email:("${value?.user?.email}")`,
        });
        if (resEmail.code == 200 && resEmail.data.length == 0) {
          isFirstTime = true;
          await saveDataToAsyncStorage('Email', value?.user?.email);
        }
      }

      const res = await CustomerActions.login({
        type: type,
        ggClientId: webClientId,
        token: type === 'google' ? value.idToken : value.identityToken,
      });
      if (res.code === 200) {
        //tạo ví
        // const customerController = new DataController('Customer');
        // try {
        //   const resultWallet = await CustomerActions.createWallet(
        //     res.data.user.Mobile ?? res.data.user.Email,
        //   );
        //   if(resultWallet && resultWallet.address && resultWallet.privateKey){
        //     await customerController.edit([
        //       {
        //         Id: res.data.user.Id,
        //         WalletAddress: resultWallet.address,
        //         PrivateKey: resultWallet.privateKey,
        //       },
        //     ]);
        //     console.log('Wallet created successfully for user:', res.data.user.Mobile);
        //   } else {
        //     console.warn('Failed to create wallet for user:', res.data.user.Mobile, 'but login continues');
        //   }
        // } catch (walletError) {
        //   console.error('Wallet creation failed:', walletError, 'but login continues');
        //   // Không hiển thị lỗi cho user vì đăng nhập đã thành công
        // }
        await saveDataToAsyncStorage(
          StorageContanst.accessToken,
          res.data.accessToken,
        );

        // Lấy thông tin user và khởi tạo socket
        await dispatch(CustomerActions.getInfor(true));

        // Khởi tạo kết nối socket sau khi đăng nhập thành công
        setTimeout(async () => {
          await AuthSocketService.initializeSocketConnection(
            res.data.user.Id,
            res.data.user.Name,
         );
        }, 1000);

        showSnackbar({
          message: 'Đăng nhập thành công',
          status: ComponentStatus.SUCCSESS,
        });

        navigateReset(RootScreen.navigateEComView, {isFirstTime: isFirstTime});
        setLoading(false);
      } else {
        showSnackbar({message: res.message, status: ComponentStatus.ERROR});
        setLoading(false);
      }
    }
  };

  return (
    <View
      style={{
        flex: 1,
        paddingTop: 35,
        width: '100%',
        height: Dimensions.get('window').height,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FLoading visible={loading} />
      <KeyboardAvoidingView
        style={{
          width: '100%',
          height: '100%',
          paddingHorizontal: 32,
          paddingTop: 32,
        }}>
        <Image
          source={require('../../assets/splash.png')}
          style={{width: 155, height: 155, alignSelf: 'center'}}
        />

        {isSignUp ? (
          <SignUpView methods={methods} />
        ) : (
          <LoginView bio={bio} methods={methods} />
        )}

        <ListTile
          title={
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <Text
                style={[
                  TypoSkin.body3,
                  {color: ColorThemes.light.neutral_text_body_color},
                ]}>
                {isSignUp
                  ? 'Chuyển sang đăng nhập ngay?'
                  : 'Bạn chưa có tài khoản?'}
              </Text>
              <AppButton
                title={isSignUp ? 'Đăng nhập' : 'Đăng ký'}
                textColor={ColorThemes.light.primary_main_color}
                textStyle={{
                  ...TypoSkin.buttonText3,
                  color: ColorThemes.light.primary_main_color,
                  // underline text
                  textDecorationLine: 'underline',
                }}
                containerStyle={{
                  height: 32,
                  borderRadius: 8,
                  paddingHorizontal: 12,
                }}
                borderColor={ColorThemes.light.transparent}
                backgroundColor={ColorThemes.light.transparent}
                onPress={() => {
                  setSignUp(!isSignUp);
                  methods.reset();
                }}
              />
            </View>
          }
          style={{
            paddingVertical: 16,
            padding: 0,
            borderRadius: 0,
            borderTopColor: ColorThemes.light.neutral_main_border_color,
            borderTopWidth: 1,
          }}
          titleStyle={[
            TypoSkin.body3,
            {color: ColorThemes.light.neutral_text_body_color},
          ]}
        />

        {/* social login */}
        {isSignUp ? null : (
          <View style={{paddingTop: 16}}>
            <View
              style={{
                flexDirection: 'row',
                paddingBottom: 16,
                width: '100%',
                justifyContent: 'center',
              }}>
              <Text
                style={[
                  TypoSkin.body3,
                  {
                    alignSelf: 'baseline',
                    color: ColorThemes.light.neutral_text_subtitle_color,
                  },
                ]}>
                Hoặc
              </Text>
            </View>
            <View style={{gap: 16}}>
              <GoogleLogin
                onLoading={setLoadingG}
                isLoading={loadingG}
                onAuthSuccess={value => _loginSocial(value, 'google')}
              />
              {/* {Platform.OS === 'ios' && (
                <AppleSignIn
                  onLoading={setLoadingA}
                  isLoading={loadingA}
                  onAuthSuccess={value => _loginSocial(value, 'apple')}
                />
              )} */}
            </View>
          </View>
        )}
      </KeyboardAvoidingView>
    </View>
  );
}

// #region Login
const LoginView = ({methods, bio}: {methods: any; bio: string}) => {
  const [isVisiblePass, setVisiblePass] = useState(true);
  const [isForgotPass, setForgotPass] = useState(false);
  const [isRemeberPass, setRemeberPass] = useState(false);
  const [loading, setLoading] = useState(false);

  const navigation = useNavigation<any>();
  const dispatch = useDispatch<any>();

  const validationForm = useMemo(() => {
    return (
      !methods.formState.errors.Mobile?.message &&
      methods.watch('Mobile')?.length > 0 &&
      methods.watch('Password')?.length > 0 &&
      !methods.formState.errors.Password?.message
    );
  }, [
    methods.watch('Mobile'),
    methods.formState.errors.Mobile?.message,
    methods.watch('Password'),
    methods.formState.errors.Password?.message,
  ]);

  useEffect(() => {
    let timeoutVariable: string | number | NodeJS.Timeout | undefined;

    if (loading) {
      timeoutVariable = setTimeout(() => setLoading(false), 60000);
    }

    return () => clearTimeout(timeoutVariable);
  }, [loading]);

  const _loginAction = async () => {
    var mobile = methods.watch('Mobile')?.trim();
    var password = methods.watch('Password')?.trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }

    const val = validatePhoneNumber(mobile);
    if (!val) {
      methods.setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
      return;
    }

    setLoading(true);
    // using password
    // check sdt da dang ky
    if (!password) return;
    if (password === undefined || password.length == 0) {
      methods.setError('Password', {message: 'Mật khẩu không được để trống'});
      return;
    }
    const valPass = regexPassWord.test(password);
    if (!valPass) {
      methods.setError('Password', {
        message: 'Mật khẩu sai định dạng, hãy thử lại',
      });
      setLoading(false);
      return;
    }
    methods.clearErrors('Password');
    setVisiblePass(true);

    const res = await CustomerActions.login({
      type: 'account',
      phone: mobile,
      password: password,
    });
    switch (res.code) {
      case 403:
        methods.setError('Password', {
          message: 'Mật khẩu không đúng, vui lòng kiểm tra lại.',
        });
        showSnackbar({
          message: 'Mật khẩu không đúng, vui lòng kiểm tra lại.',
          status: ComponentStatus.ERROR,
        });
        break;
      case 401:
        methods.setError('Mobile', {
          message: 'Số điện thoại chưa được đăng ký.',
        });
        showSnackbar({
          message: 'Số điện thoại chưa được đăng ký.',
          status: ComponentStatus.ERROR,
        });
        break;
      case 200:
        checkSavePass();
        const deviceToken = await getDataToAsyncStorage('fcmToken');
        if (deviceToken) {
          await CustomerActions.updateDeviceToken(deviceToken);
        }
        // timeRefresh 10 phut
        await saveDataToAsyncStorage(
          StorageContanst.accessToken,
          res.data.accessToken,
        );
        await saveDataToAsyncStorage(
          StorageContanst.refreshToken,
          res.data.refreshToken,
        );
        await saveDataToAsyncStorage(
          StorageContanst.timeRefresh,
          `${Date.now() / 1000 + 9 * 60}`,
        );

        await saveDataToAsyncStorage('Mobile', mobile);
        dispatch(CustomerActions.getInfor(true)).then(async (res: any) => {
          // Khởi tạo kết nối socket sau khi đăng nhập thành công
          setTimeout(async () => {
            await AuthSocketService.initializeSocketConnection();
          }, 1000);

          setLoading(false);
          showSnackbar({
            message: 'Đăng nhập thành công',
            status: ComponentStatus.SUCCSESS,
          });
          navigation.replace(RootScreen.navigateEComView);
        });
        setLoading(false);
        break;
      default:
        break;
    }
    setLoading(false);
  };

  const _forgotPassword = async () => {};

  useEffect(() => {
    getDataToAsyncStorage('RememberPass').then(rs => {
      if (rs == 'true') {
        setRemeberPass(true);
      }
    });
  }, []);

  const checkSavePass = () => {
    if (isRemeberPass) {
      saveDataToAsyncStorage('RememberPass', 'true');
      if (methods.watch('Password') && methods.watch('Password').length > 0)
        saveDataToAsyncStorage('Password', methods.watch('Password'));
    } else {
      saveDataToAsyncStorage('RememberPass', 'false');
      removeDataToAsyncStorage('Password');
    }
  };

  return (
    <TouchableWithoutFeedback
      style={{width: '100%', height: '100%'}}
      onPress={Keyboard.dismiss}>
      <View
        pointerEvents={loading ? 'none' : 'auto'}
        style={{
          width: '100%',
        }}>
        <FLoading
          visible={loading}
          avt={require('../../assets/appstore.png')}
        />

        <KeyboardAvoidingView style={{width: '100%', gap: 24}}>
          <TextFieldForm
            control={methods.control}
            name="Mobile"
            placeholder="Nhập số điện thoại của bạn"
            label="Số điện thoại"
            returnKeyType="done"
            errors={methods.formState.errors}
            textFieldStyle={{
              height: 48,
              paddingLeft: 8,
              backgroundColor: ColorThemes.light.transparent,
            }}
            register={methods.register}
            prefix={
              <View
                style={{
                  flexDirection: 'row',
                  height: 32,
                  width: 32,
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: ColorThemes.light.primary_main_color,
                  borderRadius: 100,
                }}>
                <Winicon
                  src="fill/users/contact"
                  size={12}
                  color={ColorThemes.light.neutral_absolute_background_color}
                />
              </View>
            }
            type="number-pad"
            onBlur={async (ev: string) => {
              if (ev === undefined || ev.length == 0) {
                methods.setError('Mobile', {
                  message: 'Số điện thoại không hợp lệ',
                });
                return;
              }
              var mobile = ev.trim();
              // Check if the number doesn't already start with 0 or +84
              if (!/^(\+84|0)/.test(mobile)) {
                mobile = '0' + mobile; // Add 0 at the beginning
              }
              const val = validatePhoneNumber(mobile);
              if (val) methods.clearErrors('Mobile');
              else
                methods.setError('Mobile', {
                  message: 'Số điện thoại không hợp lệ',
                });
            }}
          />
          <TextFieldForm
            control={methods.control}
            name="Password"
            label="Mật khẩu"
            prefix={
              <View
                style={{
                  flexDirection: 'row',
                  height: 32,
                  width: 32,
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: ColorThemes.light.primary_main_color,
                  borderRadius: 100,
                }}>
                <Winicon
                  src="fill/user interface/password"
                  size={12}
                  color={ColorThemes.light.neutral_absolute_background_color}
                />
              </View>
            }
            returnKeyType="done"
            placeholder={'Nhập mật khẩu của bạn'}
            errors={methods.formState.errors}
            secureTextEntry={isVisiblePass}
            textFieldStyle={{
              height: 48,
              backgroundColor: ColorThemes.light.transparent,
              paddingVertical: 16,
              paddingLeft: 8,
            }}
            register={methods.register}
            suffix={
              <TouchableOpacity
                style={{padding: 12}}
                onPress={() => {
                  setVisiblePass(!isVisiblePass);
                }}>
                <Winicon
                  src={
                    isVisiblePass
                      ? `outline/user interface/view`
                      : `outline/user interface/hide`
                  }
                  size={14}
                />
              </TouchableOpacity>
            }
            onBlur={async (ev: string) => {
              var pass = ev.trim();
              if (!regexPassWord.test(pass))
                return methods.setError('Password', {
                  message: 'Mật khẩu sai định dạng, hãy thử lại',
                });
              methods.clearErrors('Password');
            }}
          />
        </KeyboardAvoidingView>

        <View
          style={{
            paddingTop: methods.formState.errors.Password ? 12 : 0,
            flexDirection: 'row',
            width: '100%',
            alignItems: 'center',
          }}>
          <TouchableOpacity
            onPress={async () => {
              setRemeberPass(!isRemeberPass);
            }}
            style={{
              paddingVertical: 8,
              alignItems: 'center',
              gap: 8,
              flexDirection: 'row',
            }}>
            <Checkbox
              onChange={vl => {
                saveDataToAsyncStorage('RememberPass', vl ? 'true' : 'false');
                setRemeberPass(vl);
              }}
              value={isRemeberPass}
            />
            <Text
              style={[
                TypoSkin.body3,
                {
                  color: isRemeberPass
                    ? ColorThemes.light.primary_main_color
                    : ColorThemes.light.neutral_text_subtitle_color,
                },
              ]}>
              Ghi nhớ mật khẩu
            </Text>
          </TouchableOpacity>
          <View style={{flex: 1}} />
          {/* <TouchableOpacity
            onPress={() => {
              var mobile = methods.watch('Mobile')?.trim();
              // Check if the number doesn't already start with 0 or +84
              if (
                mobile &&
                !/^(\+84|0)/.test(mobile) &&
                !mobile?.includes('@')
              ) {
                mobile = '0' + mobile; // Add 0 at the beginning
              }
              navigation.navigate(RootScreen.ForgotPass, {
                isLogin: true,
                mobile: mobile || '',
              });
            }}
            style={{
              paddingVertical: 8,
              alignItems: 'center',
            }}>
            <Text
              style={[
                TypoSkin.body3,
                {
                  color: ColorThemes.light.neutral_text_subtitle_color,
                },
              ]}>
              Quên mật khẩu?
            </Text>
          </TouchableOpacity> */}
          <View />
        </View>

        <View
          style={{
            flexDirection: 'row',
            width: '100%',
            gap: 8,
            alignItems: 'center',
          }}>
          <AppButton
            title={'Đăng nhập'}
            textColor={ColorThemes.light.neutral_absolute_background_color}
            textStyle={{
              ...TypoSkin.buttonText1,
              color: ColorThemes.light.white,
            }}
            disabled={!validationForm}
            containerStyle={{
              height: 48,
              flex: 1,
              borderRadius: 8,
              marginTop: 8,
            }}
            borderColor={ColorThemes.light.neutral_main_border_color}
            backgroundColor={ColorThemes.light.primary_main_color}
            onPress={() => {
              _loginAction();
            }}
          />
          {bio == 'true' ? (
            <View style={{paddingTop: 8}}>
              <LocalAuthen
                isFirstTime={
                  methods.watch('LastMobile') !== methods.watch('Mobile') &&
                  methods.watch('Mobile')?.length != 0
                }
                onSuccess={async value => {
                  if (value === true) {
                    var mobile = await getDataToAsyncStorage('Mobile');
                    var password = await getDataToAsyncStorage('Password');
                    if (!mobile || !password) return;
                    // Check if the number doesn't already start with 0 or +84
                    if (/^(\+84|0)/.test(mobile) && !mobile.includes('@')) {
                      const val = validatePhoneNumber(mobile);
                      if (!val) {
                        methods.setError('Mobile', {
                          message: 'Số điện thoại không hợp lệ',
                        });
                        return;
                      }
                    }
                    setLoading(true);
                    const res = await CustomerActions.login({
                      type: 'account',
                      email: mobile.includes('@') ? mobile : undefined,
                      password: password ? password : undefined,
                      phone: !mobile.includes('@') ? mobile : undefined,
                    });

                    if (res.code === 200) {
                      // saveDataToAsyncStorage(
                      //   'timeRefresh',
                      //   `${Date.now() / 1000 + 9 * 60}`,
                      // );
                      // saveDataToAsyncStorage(
                      //   'accessToken',
                      //   `${res.data.accessToken}`,
                      // );
                      // saveDataToAsyncStorage(
                      //   'refreshToken',
                      //   `${res.data.refreshToken}`,
                      // );
                      saveDataToAsyncStorage('Mobile', `${mobile}`);

                      // Lấy thông tin user và khởi tạo socket
                      await dispatch(CustomerActions.getInfor(true));

                      // Khởi tạo kết nối socket sau khi đăng nhập thành công
                      setTimeout(async () => {
                        await AuthSocketService.initializeSocketConnection();
                      }, 1000);

                      showSnackbar({
                        message:
                          'Đăng nhập thành công, Chào mừng bạn đã quay trở lại',
                        status: ComponentStatus.SUCCSESS,
                      });
                      setLoading(false);
                      navigateReset(RootScreen.navigateEComView);
                    } else {
                      showSnackbar({
                        message: res.message,
                        status: ComponentStatus.ERROR,
                      });
                      setLoading(false);
                    }
                    setLoading(false);
                  }
                }}
              />
            </View>
          ) : null}
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

// #region SignUp
const SignUpView = ({methods}: {methods: any}) => {
  const [isVisiblePass, setVisiblePass] = useState(true);
  const [isVisiblePass2, setVisiblePass2] = useState(true);
  const [loading, setLoading] = useState(false);
  const dialogRef = useRef<any>(null);
  const navigation = useNavigation<any>();
  const dispatch = useDispatch<any>();

  const validationForm = useMemo(() => {
    return (
      !methods.formState.errors.Mobile?.message &&
      methods.watch('Mobile')?.length > 0 &&
      methods.watch('Password')?.length > 0 &&
      !methods.formState.errors.Password?.message &&
      methods.watch('ConfirmPassword')?.length > 0 &&
      !methods.formState.errors.ConfirmPassword?.message
    );
  }, [
    methods.watch('Mobile'),
    methods.formState.errors.Mobile?.message,
    methods.watch('Password'),
    methods.formState.errors.Password?.message,
    methods.watch('ConfirmPassword'),
    methods.formState.errors.ConfirmPassword?.message,
  ]);

  useEffect(() => {
    methods.setValue('Mobile', undefined);
    methods.setValue('Password', undefined);
    setLoading(false);
  }, []);

  useEffect(() => {
    let timeoutVariable: string | number | NodeJS.Timeout | undefined;

    if (loading) {
      timeoutVariable = setTimeout(() => setLoading(false), 60000);
    }

    return () => clearTimeout(timeoutVariable);
  }, [loading]);

  const onCheckPhoneForOtp = async () => {
    var mobile = methods.watch('Mobile').trim();
    var email = methods.watch('Email').trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }
    if (!validatePhoneNumber(mobile)) {
      methods.setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
      return false;
    }

    setLoading(true);

    // check sdt bi khoa
    // check sdt da dang ky
    const resCustomers = await customerController.getListSimple({
      page: 1,
      size: 1,
      query: `@Mobile:(${mobile}) | @Email:(${email})`,
    });
    if (resCustomers) {
      if (resCustomers?.data?.length > 0) {
        // check sdt bi khoa
        if (resCustomers?.data[0]?.Status == CustomerStatus.locked) {
          showSnackbar({
            message:
              'Tài khoản của bạn đã bị khóa, vui lòng liên hệ với quản trị viên để được hỗ trợ.',
            status: ComponentStatus.ERROR,
          });
          setLoading(false);
          return false;
        }
        showSnackbar({
          message: 'Tài khoản đã đăng ký trước đó',
          status: ComponentStatus.ERROR,
        });
        setLoading(false);
        return false;
      }
    }
    setLoading(false);
    return true;
  };

  const customerController = new DataController('Customer');
  const scrollRef = useRef<any>(null);

  const _signUp = async () => {
    var mobile = methods.watch('Mobile')?.trim();
    var password = methods.watch('Password')?.trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }

    const val = validatePhoneNumber(mobile);
    if (!val) {
      methods.setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
      return;
    }

    setLoading(true);

    const deviceToken = await getDataToAsyncStorage('fcmToken');
    const hashPass = await CustomerActions.hashPassword(password);

    if (hashPass.code != 200) {
      setLoading(false);
      showSnackbar({
        message: hashPass.message,
        status: ComponentStatus.ERROR,
      });
      return;
    }

    //lấy thông tin theo ref code
    var parentId;
    var listParent;
    if (methods.watch('RefCode')?.length > 0) {
      const resRef = await customerController.getListSimple({
        page: 1,
        size: 1,
        query: `@RefCode: (*${methods.watch('RefCode')}*)`,
        returns: ['Id', 'Name', 'AvatarUrl', 'ListParent'],
      });
      if (resRef.code === 200 && resRef.data.length > 0) {
        parentId = resRef.data[0].Id;
        listParent = resRef.data[0].ListParent
          ? resRef.data[0].ListParent + ',' + resRef.data[0].Id
          : resRef.data[0].Id;
      }
    }
    // #region Tạo ví blockchain
    //TODO: Tạo ví blockchain
    var walletAddress = '';
    var privateKey = '';

    // Mã hóa private key
    // TODO: Sẽ enable lại sau khi fix build issue

    //end tạo ví
    // #endregion
    const newCus = {
      Id: randomGID(),
      Name: methods.watch('Name'),
      Email: methods.watch('Email'),
      Address: methods.watch('Address'),
      Long: methods.watch('Long'),
      Lat: methods.watch('Lat'),
      DateCreated: Date.now(),
      Mobile: mobile,
      Status: CustomerStatus.active,
      Password: hashPass.data,
      DeviceToken: deviceToken,
      RefCode: Ultis.randomString(10),
      ParentId: parentId,
      ListParent: listParent,
    };
    console.log('newCus', newCus);
    const customerRes = await customerController.add([newCus]);
    if (customerRes.code == 200) {
      // Đăng nhập trước để đảm bảo user có thể sử dụng app
      const res = await CustomerActions.login({
        type: 'account',
        phone: mobile,
        password: password,
      });

      if (res.code === 200) {
        // saveDataToAsyncStorage('timeRefresh', `${Date.now() / 1000 + 9 * 60}`);
        // saveDataToAsyncStorage('accessToken', `${res.data.accessToken}`);
        // saveDataToAsyncStorage('refreshToken', `${res.data.refreshToken}`);
        saveDataToAsyncStorage('Mobile', `${mobile}`);
        removeDataToAsyncStorage('Password');
        removeDataToAsyncStorage('RememberPass');
        // Tạo ví trong background - không ảnh hưởng đến đăng nhập
        try {
          const resultWallet = await CustomerActions.createWallet(mobile);
          if (resultWallet && resultWallet.address && resultWallet.privateKey) {
            await customerController.edit([
              {
                Id: newCus.Id,
                WalletAddress: resultWallet.address,
                PrivateKey: resultWallet.privateKey,
              },
            ]);
            console.log('Wallet created successfully for user:', mobile);
          } else {
            console.warn(
              'Failed to create wallet for user:',
              mobile,
              'but login continues',
            );
          }
        } catch (walletError) {
          console.error(
            'Wallet creation failed:',
            walletError,
            'but login continues',
          );
          // Không hiển thị lỗi cho user vì đăng nhập đã thành công
        }

        dispatch(CustomerActions.getInfor()).then(() => {
          setLoading(false);
          showSnackbar({
            message: 'Tạo tài khoản và đăng nhập thành công',
            status: ComponentStatus.SUCCSESS,
          });
          navigation.replace(RootScreen.navigateEComView);
        });
      } else {
        showSnackbar({message: res.message, status: ComponentStatus.ERROR});
        setLoading(false);
      }
    } else {
      showSnackbar({
        message: customerRes.message ?? 'Đã có lỗi xảy ra khi tạo tài khoản.',
        status: ComponentStatus.ERROR,
      });
      setLoading(false);
      return;
    }
    setLoading(false);
  };

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      style={{width: '100%'}}
      ref={scrollRef}>
      <FLoading visible={loading} avt={require('../../assets/appstore.png')} />
      <FDialog ref={dialogRef} />
      <Pressable>
        <KeyboardAvoidingView
          behavior={'padding'}
          style={{width: '100%', marginTop: 8}}>
          <View style={{width: '100%', gap: 16}}>
            <TextFieldForm
              control={methods.control}
              name="Name"
              placeholder="Họ và tên"
              label="Họ và tên"
              returnKeyType="done"
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                paddingVertical: 16,
                paddingLeft: 8,
                backgroundColor: ColorThemes.light.transparent,
              }}
              register={methods.register}
              type="name-phone-pad"
              onBlur={(ev: string) => {
                if (ev?.length !== 0) methods.clearErrors('Name');
                else
                  methods.setError('Name', {
                    message: 'Họ và tên không được để trống',
                  });
              }}
              prefix={
                <View
                  style={{
                    flexDirection: 'row',
                    height: 32,
                    width: 32,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: ColorThemes.light.primary_main_color,
                    borderRadius: 100,
                  }}>
                  <Winicon
                    src="fill/users/contact"
                    size={12}
                    color={ColorThemes.light.neutral_absolute_background_color}
                  />
                </View>
              }
            />
            <TextFieldForm
              control={methods.control}
              name="Email"
              required
              placeholder="Email"
              label="Email"
              returnKeyType="done"
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                paddingVertical: 16,
                paddingLeft: 8,
                backgroundColor: ColorThemes.light.transparent,
              }}
              register={methods.register}
              type="email-address"
              onBlur={(ev: string) => {
                if (ev?.length !== 0) methods.clearErrors('Email');
                else
                  methods.setError('Email', {
                    message: 'Email không được để trống',
                  });
              }}
              prefix={
                <View
                  style={{
                    flexDirection: 'row',
                    height: 32,
                    width: 32,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: ColorThemes.light.primary_main_color,
                    borderRadius: 100,
                  }}>
                  <Winicon
                    src="fill/users/contact"
                    size={12}
                    color={ColorThemes.light.neutral_absolute_background_color}
                  />
                </View>
              }
            />
            <TextFieldForm
              control={methods.control}
              name="Mobile"
              required
              label="Số điện thoại"
              placeholder="Nhập số điện thoại của bạn"
              returnKeyType="done"
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                paddingLeft: 8,
                backgroundColor: ColorThemes.light.transparent,
              }}
              register={methods.register}
              prefix={
                <View
                  style={{
                    flexDirection: 'row',
                    height: 32,
                    width: 32,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: ColorThemes.light.primary_main_color,
                    borderRadius: 100,
                  }}>
                  <Winicon
                    src="fill/user interface/phone"
                    size={12}
                    color={ColorThemes.light.neutral_absolute_background_color}
                  />
                </View>
              }
              type="number-pad"
              onBlur={async (ev: string) => {
                if (ev === undefined || ev.length == 0) {
                  methods.setError('Mobile', {
                    message: 'Số điện thoại không hợp lệ',
                  });
                  return;
                }
                var mobile = ev.trim();
                // Check if the number doesn't already start with 0 or +84
                if (!/^(\+84|0)/.test(mobile)) {
                  mobile = '0' + mobile; // Add 0 at the beginning
                }
                const val = validatePhoneNumber(mobile);
                if (val) methods.clearErrors('Mobile');
                else
                  methods.setError('Mobile', {
                    message: 'Số điện thoại không hợp lệ',
                  });
              }}
            />
            <View style={{width: '100%', gap: 8}}>
              <TextFieldForm
                control={methods.control}
                name="Password"
                required
                secureTextEntry={isVisiblePass}
                label="Mật khẩu"
                returnKeyType="done"
                prefix={
                  <View
                    style={{
                      flexDirection: 'row',
                      height: 32,
                      width: 32,
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: ColorThemes.light.primary_main_color,
                      borderRadius: 100,
                    }}>
                    <Winicon
                      src="fill/user interface/password"
                      size={12}
                      color={
                        ColorThemes.light.neutral_absolute_background_color
                      }
                    />
                  </View>
                }
                placeholder={'Tạo mật khẩu của bạn'}
                suffix={
                  <TouchableOpacity
                    style={{padding: 12}}
                    onPress={() => {
                      setVisiblePass(!isVisiblePass);
                    }}>
                    <Winicon
                      src={
                        isVisiblePass
                          ? `outline/user interface/view`
                          : `outline/user interface/hide`
                      }
                      size={14}
                    />
                  </TouchableOpacity>
                }
                errors={methods.formState.errors}
                textFieldStyle={{
                  height: 48,
                  backgroundColor: ColorThemes.light.transparent,
                  paddingLeft: 8,
                  paddingVertical: 16,
                  marginBottom: methods.formState.errors.Password?.message
                    ? 16
                    : 8,
                }}
                register={methods.register}
                onBlur={async (ev: string) => {
                  if (ev === undefined || ev.length == 0) {
                    methods.setError('Password', {
                      message: 'Mật khẩu không được để trống',
                    });
                    return;
                  }
                  var pass = ev.trim();
                  if (!regexPassWord.test(pass))
                    return methods.setError('Password', {
                      message: 'Mật khẩu sai định dạng, hãy thử lại',
                    });
                  methods.clearErrors('Password');
                }}
              />
              <Text
                style={[
                  TypoSkin.subtitle4,
                  {
                    alignSelf: 'baseline',
                    color: ColorThemes.light.neutral_text_subtitle_color,
                  },
                ]}>{`- Tối thiểu 8 ký tự/ Tối đa 16 ký tự \n- Gồm chữ hoa, thường và số`}</Text>
              <TextFieldForm
                control={methods.control}
                name="ConfirmPassword"
                required
                label="Nhập lại mật khẩu"
                returnKeyType="done"
                secureTextEntry={isVisiblePass2}
                suffix={
                  <TouchableOpacity
                    style={{padding: 12}}
                    onPress={() => {
                      setVisiblePass2(!isVisiblePass2);
                    }}>
                    <Winicon
                      src={
                        isVisiblePass2
                          ? `outline/user interface/view`
                          : `outline/user interface/hide`
                      }
                      size={14}
                    />
                  </TouchableOpacity>
                }
                prefix={
                  <View
                    style={{
                      flexDirection: 'row',
                      height: 32,
                      width: 32,
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: ColorThemes.light.primary_main_color,
                      borderRadius: 100,
                    }}>
                    <Winicon
                      src="fill/user interface/password"
                      size={12}
                      color={
                        ColorThemes.light.neutral_absolute_background_color
                      }
                    />
                  </View>
                }
                placeholder={'Nhập lại mật khẩu của bạn'}
                errors={methods.formState.errors}
                textFieldStyle={{
                  height: 48,
                  backgroundColor: ColorThemes.light.transparent,
                  paddingLeft: 8,
                  paddingVertical: 16,
                }}
                register={methods.register}
                onBlur={async (ev: string) => {
                  if (ev === undefined || ev.length == 0) {
                    methods.setError('ConfirmPassword', {
                      message: 'Mật khẩu không được để trống',
                    });
                    return;
                  }
                  var rePass = ev.trim();
                  if (methods.watch('Password') !== rePass)
                    return methods.setError('ConfirmPassword', {
                      message: 'Mật khẩu nhập lại không đúng',
                    });
                  methods.clearErrors('ConfirmPassword');
                }}
              />
            </View>
            <FAddressPickerForm
              control={methods.control}
              errors={methods.formState.errors}
              name="Address"
              label="Địa chỉ"
              prefix={
                <View
                  style={{
                    flexDirection: 'row',
                    height: 32,
                    width: 32,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: ColorThemes.light.primary_main_color,
                    borderRadius: 100,
                  }}>
                  <Winicon
                    src="fill/location/map-marker"
                    size={12}
                    color={ColorThemes.light.neutral_absolute_background_color}
                  />
                </View>
              }
              placeholder="Nhập địa chỉ của bạn"
              onChange={value => {
                methods.setValue('Long', value.geometry.location.lng);
                methods.setValue('Lat', value.geometry.location.lat);
                methods.setValue('Address', value.formatted_address);
                return value.formatted_address;
              }}
              textFieldStyle={{
                paddingLeft: 8,
                gap: 12,
              }}
            />
            <TextFieldForm
              control={methods.control}
              name="RefCode"
              placeholder="Mã giới thiệu (Nếu có)"
              label="Mã giới thiệu (Nếu có)"
              returnKeyType="done"
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                padding: 16,
                backgroundColor: ColorThemes.light.transparent,
              }}
              register={methods.register}
              type="name-phone-pad"
              onBlur={(ev: string) => {
                if (ev?.length !== 0) methods.clearErrors('Name');
                else
                  methods.setError('Name', {
                    message: 'Họ và tên không được để trống',
                  });
              }}
            />
          </View>
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              paddingTop: 16,
              paddingBottom: 130,
            }}>
            <AppButton
              title={'Đăng ký'}
              textColor={ColorThemes.light.neutral_absolute_background_color}
              textStyle={{
                ...TypoSkin.buttonText1,
                color: ColorThemes.light.neutral_absolute_background_color,
              }}
              disabled={!validationForm || loading}
              containerStyle={{
                height: 48,
                flex: 1,
                borderRadius: 8,
                marginTop: 8,
              }}
              borderColor={ColorThemes.light.neutral_main_border_color}
              backgroundColor={ColorThemes.light.primary_main_color}
              onPress={async () => {
                methods.handleSubmit(async (data: any) => {
                  if (loading) return;
                  const rs = await onCheckPhoneForOtp();
                  if (rs) {
                    _signUp();
                  }
                })();
              }}
            />
          </View>
        </KeyboardAvoidingView>
      </Pressable>
    </ScrollView>
  );
};
