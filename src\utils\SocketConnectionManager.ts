/**
 * Global Socket Connection Manager
 * Đảm bảo chỉ có 1 socket connection duy nhất trong toàn bộ app
 */

class SocketConnectionManager {
  private static instance: SocketConnectionManager;
  private isConnecting = false;
  private connectionPromise: Promise<boolean> | null = null;
  private currentUserId: string | null = null;

  private constructor() {}

  static getInstance(): SocketConnectionManager {
    if (!SocketConnectionManager.instance) {
      SocketConnectionManager.instance = new SocketConnectionManager();
    }
    return SocketConnectionManager.instance;
  }

  /**
   * Kết nối socket với cơ chế singleton
   * Nếu đang có connection process thì return promise hiện tại
   */
  async connect(userId: string, name: string, forceReconnect = false): Promise<boolean> {
    // Nếu đang kết nối, return promise hiện tại
    if (this.isConnecting && this.connectionPromise) {
      console.log('⏳ [SocketConnectionManager] Connection in progress, waiting...');
      return this.connectionPromise;
    }

    // Kiểm tra connection hiện tại
    const SocketService = require('../modules/chat/services/SocketService').default;
    const isCurrentlyConnected = SocketService.getConnectionStatus();

    // Nếu đã kết nối với cùng user và socket vẫn active
    if (!forceReconnect && this.currentUserId === userId && isCurrentlyConnected) {
      console.log('✅ [SocketConnectionManager] Already connected for same user and socket is active');
      return true;
    }

    // Log lý do reconnect
    if (this.currentUserId !== userId) {
      console.log(`🔄 [SocketConnectionManager] Different user: ${this.currentUserId} → ${userId}`);
    } else if (!isCurrentlyConnected) {
      console.log('🔄 [SocketConnectionManager] Socket disconnected, reconnecting...');
    } else if (forceReconnect) {
      console.log('🔄 [SocketConnectionManager] Force reconnect requested');
    }

    // Bắt đầu process kết nối mới
    this.isConnecting = true;
    this.connectionPromise = this.performConnection(userId, name);

    try {
      const result = await this.connectionPromise;
      if (result) {
        this.currentUserId = userId;
      }
      return result;
    } finally {
      this.isConnecting = false;
      this.connectionPromise = null;
    }
  }

  private async performConnection(userId: string, name: string): Promise<boolean> {
    try {
      console.log(`🔌 [SocketConnectionManager] Performing connection for user: ${userId}`);

      // Import services
      const SocketService = require('../modules/chat/services/SocketService').default;
      const { connectSocket } = require('../redux/reducers/ChatReducer');
      const { store } = require('../redux/store/store');

      // Kết nối socket trực tiếp
      await SocketService.connect(userId, name);

      // Cập nhật Redux state
      store.dispatch(connectSocket(userId, name));

      console.log('✅ [SocketConnectionManager] Connection completed successfully');
      return true;
    } catch (error) {
      console.error('❌ [SocketConnectionManager] Connection failed:', error);
      return false;
    }
  }

  /**
   * Ngắt kết nối
   */
  disconnect(): void {
    this.isConnecting = false;
    this.connectionPromise = null;
    this.currentUserId = null;
    
    const AuthSocketService = require('../services/AuthSocketService').default;
    AuthSocketService.disconnectSocket();
  }

  /**
   * Kiểm tra trạng thái
   */
  isCurrentlyConnecting(): boolean {
    return this.isConnecting;
  }

  getCurrentUserId(): string | null {
    return this.currentUserId;
  }

  /**
   * Kiểm tra xem có đang connected với user này không
   */
  isConnectedForUser(userId: string): boolean {
    const SocketService = require('../modules/chat/services/SocketService').default;
    return this.currentUserId === userId && SocketService.getConnectionStatus();
  }

  /**
   * Reset state (dùng cho testing hoặc cleanup)
   */
  reset(): void {
    this.isConnecting = false;
    this.connectionPromise = null;
    this.currentUserId = null;
  }
}

export default SocketConnectionManager.getInstance();
