/**
 * Global Socket Connection Manager
 * <PERSON><PERSON><PERSON> bảo chỉ có 1 socket connection duy nhất trong toàn bộ app
 */

class SocketConnectionManager {
  private static instance: SocketConnectionManager;
  private isConnecting = false;
  private connectionPromise: Promise<boolean> | null = null;
  private currentUserId: string | null = null;

  private constructor() {}

  static getInstance(): SocketConnectionManager {
    if (!SocketConnectionManager.instance) {
      SocketConnectionManager.instance = new SocketConnectionManager();
    }
    return SocketConnectionManager.instance;
  }

  /**
   * Kết nối socket với cơ chế singleton
   * Nếu đang có connection process thì return promise hiện tại
   */
  async connect(userId: string, name: string, forceReconnect = false): Promise<boolean> {
    // Nếu đang kết nối, return promise hiện tại
    if (this.isConnecting && this.connectionPromise) {
      console.log('⏳ [SocketConnectionManager] Connection in progress, waiting...');
      return this.connectionPromise;
    }

    // Nếu đã kết nối với cùng user và không force reconnect
    if (!forceReconnect && this.currentUserId === userId) {
      console.log('✅ [SocketConnectionManager] Already connected for same user');
      return true;
    }

    // Bắt đầu process kết nối mới
    this.isConnecting = true;
    this.connectionPromise = this.performConnection(userId, name);

    try {
      const result = await this.connectionPromise;
      if (result) {
        this.currentUserId = userId;
      }
      return result;
    } finally {
      this.isConnecting = false;
      this.connectionPromise = null;
    }
  }

  private async performConnection(userId: string, name: string): Promise<boolean> {
    try {
      console.log(`🔌 [SocketConnectionManager] Performing connection for user: ${userId}`);

      // Import services
      const SocketService = require('../modules/chat/services/SocketService').default;
      const { connectSocket } = require('../redux/reducers/ChatReducer');
      const { store } = require('../redux/store/store');

      // Kết nối socket trực tiếp
      await SocketService.connect(userId, name);

      // Cập nhật Redux state
      store.dispatch(connectSocket(userId, name));

      console.log('✅ [SocketConnectionManager] Connection completed successfully');
      return true;
    } catch (error) {
      console.error('❌ [SocketConnectionManager] Connection failed:', error);
      return false;
    }
  }

  /**
   * Ngắt kết nối
   */
  disconnect(): void {
    this.isConnecting = false;
    this.connectionPromise = null;
    this.currentUserId = null;
    
    const AuthSocketService = require('../services/AuthSocketService').default;
    AuthSocketService.disconnectSocket();
  }

  /**
   * Kiểm tra trạng thái
   */
  isCurrentlyConnecting(): boolean {
    return this.isConnecting;
  }

  getCurrentUserId(): string | null {
    return this.currentUserId;
  }

  /**
   * Reset state (dùng cho testing hoặc cleanup)
   */
  reset(): void {
    this.isConnecting = false;
    this.connectionPromise = null;
    this.currentUserId = null;
  }
}

export default SocketConnectionManager.getInstance();
